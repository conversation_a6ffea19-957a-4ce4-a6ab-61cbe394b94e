import React from 'react';

const OverallScoreTable = ({ currentScore }) => {
    const scoreRanges = [
        {
            range: "< 59.9",
            color: "red",
            category: "Major Non-Compliance",
            description: "There is a systemic failure or major non-compliance in relation to the project or DC operation's state of EHS that requires management intervention and action. The required EHS standards and/or processes have not been implemented, or have been ineffective or unsuitable in application, or contravention(s) of legislative requirements have been observed, leading to the potential risk of exposure to a LARGE or VERY LARGE impact."
        },
        {
            range: "60 - 79.9",
            color: "#e29e09",
            category: "Minor Non-Compliance",
            description: "Non-compliance of medium or low risks identified, with conditions/situations controllable with remedial action implemented to meet compliance. The required EHS standards and/or process have not been fully implemented, or have been in place but the application or implementation requires amendments/improvement, or minor deviations from legislative requirements have been observed, leading to the potential risk of exposure to LOW or MEDIUM impact."
        },
        {
            range: "≥ 80",
            color: "green",
            category: "Compliant",
            description: "The required EHS standards and/or processes have been implemented to a good/acceptable level and meet STT GDC and legislative requirements. Minor issues/improvement opportunities that may have been observed are of relatively VERY LOW impact, with issues controllable with immediate corrective actions implemented."
        }
    ];

    const getRowStyle = (scoreColor) => {
        if (!currentScore) return {};
        
        const score = parseFloat(currentScore);
        let isCurrentRange = false;
        
        if (scoreColor === "red" && score < 59.9) {
            isCurrentRange = true;
        } else if (scoreColor === "#e29e09" && score >= 60 && score <= 79.9) {
            isCurrentRange = true;
        } else if (scoreColor === "green" && score >= 80) {
            isCurrentRange = true;
        }
        
        return isCurrentRange ? { 
            backgroundColor: scoreColor === "#e29e09" ? "#fff3cd" : scoreColor === "red" ? "#f8d7da" : "#d1edff",
            fontWeight: "bold"
        } : {};
    };

    return (
        <div className="overall-score-table-container mb-4">
            <table className="table table-bordered">
                <thead>
                    <tr style={{ backgroundColor: '#58595b', color: 'white' }}>
                        <th style={{ width: '15%' }}>Overall Score</th>
                        <th style={{ width: '20%' }}>Colour Category</th>
                        <th style={{ width: '65%' }}>Description</th>
                    </tr>
                </thead>
                <tbody>
                    {scoreRanges.map((range, index) => (
                        <tr key={index} style={getRowStyle(range.color)}>
                            <td style={{ textAlign: 'center', fontWeight: 'bold' }}>
                                {range.range}
                            </td>
                            <td style={{ 
                                backgroundColor: range.color, 
                                color: range.color === '#e29e09' ? 'black' : 'white',
                                textAlign: 'center',
                                fontWeight: 'bold'
                            }}>
                                {range.category}
                            </td>
                            <td style={{ fontSize: '14px', lineHeight: '1.4' }}>
                                {range.description}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default OverallScoreTable;
